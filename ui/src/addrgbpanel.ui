<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <author><PERSON><PERSON></author>
 <comment>
  Q Light Controller Plus
  addrgbpanel.ui

  Copyright (c) 2015 Massimo <PERSON>ari

  Licensed under the Apache License, Version 2.0 (the &quot;License&quot;);
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0.txt

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an &quot;AS IS&quot; BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
 </comment>
 <class>AddRGBPanel</class>
 <widget class="QDialog" name="AddRGBPanel">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>477</width>
    <height>452</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Add RGB Panel</string>
  </property>
  <property name="windowIcon">
   <iconset resource="qlcui.qrc">
    <normaloff>:/rgbpanel.png</normaloff>:/rgbpanel.png</iconset>
  </property>
  <layout class="QGridLayout" name="gridLayout_4">
   <item row="0" column="0" colspan="2">
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>Panel properties</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <layout class="QGridLayout" name="gridLayout">
        <item row="1" column="0">
         <widget class="QLabel" name="label_2">
          <property name="text">
           <string>Universe</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QLineEdit" name="m_nameEdit">
          <property name="text">
           <string>RGB Panel</string>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="label">
          <property name="text">
           <string>Name</string>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QComboBox" name="m_uniCombo"/>
        </item>
        <item row="2" column="0">
         <widget class="QLabel" name="label_6">
          <property name="text">
           <string>Address</string>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QSpinBox" name="m_addressSpin">
          <property name="minimum">
           <number>1</number>
          </property>
          <property name="maximum">
           <number>511</number>
          </property>
         </widget>
        </item>
        <item row="3" column="1">
         <widget class="QLabel" name="m_addrErrorLabel">
          <property name="text">
           <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; color:#ff0000;&quot;&gt;ERROR: Address already used!&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
          </property>
         </widget>
        </item>
        <item row="4" column="0">
         <widget class="QLabel" name="label_11">
          <property name="text">
           <string>Components</string>
          </property>
         </widget>
        </item>
        <item row="4" column="1">
         <widget class="QComboBox" name="m_compCombo"/>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="1">
    <widget class="QGroupBox" name="groupBox_5">
     <property name="title">
      <string>Physical</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <layout class="QGridLayout" name="gridLayout_5">
        <property name="verticalSpacing">
         <number>0</number>
        </property>
        <item row="0" column="0">
         <widget class="QLabel" name="label_7">
          <property name="text">
           <string>Width</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QSpinBox" name="m_phyWidthSpin">
          <property name="suffix">
           <string>mm</string>
          </property>
          <property name="minimum">
           <number>1</number>
          </property>
          <property name="maximum">
           <number>99999</number>
          </property>
          <property name="value">
           <number>100</number>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QLabel" name="label_8">
          <property name="text">
           <string>Height</string>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QSpinBox" name="m_phyHeightSpin">
          <property name="suffix">
           <string>mm</string>
          </property>
          <property name="minimum">
           <number>1</number>
          </property>
          <property name="maximum">
           <number>99999</number>
          </property>
          <property name="value">
           <number>100</number>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="3" column="0" colspan="2">
    <widget class="QCheckBox" name="m_skipIncompletePixelsCb">
     <property name="toolTip">
      <string>Skip fixtures that would span across DMX universe boundaries</string>
     </property>
     <property name="text">
      <string>Skip incomplete pixels</string>
     </property>
    </widget>
   </item>
   <item row="4" column="0" colspan="2">
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="QGroupBox" name="groupBox_2">
     <property name="title">
      <string>Size</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="0" column="0">
         <widget class="QLabel" name="label_3">
          <property name="text">
           <string>Columns</string>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QLabel" name="label_4">
          <property name="text">
           <string>Rows</string>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QLabel" name="label_5">
          <property name="text">
           <string>Total pixels</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QSpinBox" name="m_columnSpin">
          <property name="minimum">
           <number>1</number>
          </property>
          <property name="maximum">
           <number>170</number>
          </property>
          <property name="value">
           <number>10</number>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QSpinBox" name="m_rowSpin">
          <property name="minimum">
           <number>1</number>
          </property>
          <property name="maximum">
           <number>999</number>
          </property>
          <property name="value">
           <number>10</number>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QLabel" name="m_totalLabel">
          <property name="text">
           <string notr="true">100</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="2" column="1" rowspan="2">
    <layout class="QVBoxLayout" name="verticalLayout_4">
     <item>
      <widget class="QGroupBox" name="groupBox_3">
       <property name="title">
        <string>Displacement</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout">
        <item>
         <widget class="QRadioButton" name="m_snakeRadio">
          <property name="text">
           <string>Snake</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QRadioButton" name="m_zigzagRadio">
          <property name="text">
           <string>Zig Zag</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="groupBox_6">
       <property name="title">
        <string>Direction</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <widget class="QRadioButton" name="m_horizontalRadio">
          <property name="text">
           <string>Horizontal</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QRadioButton" name="m_verticalRadio">
          <property name="text">
           <string>Vertical</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item row="2" column="0" rowspan="2">
    <widget class="QGroupBox" name="groupBox_4">
     <property name="title">
      <string>Orientation</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_3">
      <item row="0" column="1">
       <widget class="QRadioButton" name="m_oriTopRightRadio">
        <property name="text">
         <string>Top-Right</string>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QRadioButton" name="m_oriTopLeftRadio">
        <property name="text">
         <string>Top-Left</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QRadioButton" name="m_oriBottomLeftRadio">
        <property name="text">
         <string>Bottom-Left</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QRadioButton" name="m_oriBottomRightRadio">
        <property name="text">
         <string>Bottom-Right</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="qlcui.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>AddRGBPanel</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>AddRGBPanel</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
