<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <author><PERSON><PERSON></author>
 <comment>
  Q Light Controller Plus
  rgbmatrixeditor.ui

  Copyright (c) 2015 Massimo <PERSON>ari

  Licensed under the Apache License, Version 2.0 (the &quot;License&quot;);
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0.txt

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an &quot;AS IS&quot; BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
 </comment>
 <class>RGBMatrixEditor</class>
 <widget class="QWidget" name="RGBMatrixEditor">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>575</width>
    <height>728</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>RGB Matrix Editor</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="topMargin">
    <number>4</number>
   </property>
   <property name="bottomMargin">
    <number>4</number>
   </property>
   <property name="spacing">
    <number>5</number>
   </property>
   <item row="2" column="0" colspan="3">
    <widget class="QGraphicsView" name="m_preview"/>
   </item>
   <item row="2" column="3" rowspan="3">
    <layout class="QVBoxLayout" name="verticalLayout_5">
     <item>
      <widget class="QGroupBox" name="m_patternGroup">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="title">
        <string>Pattern</string>
       </property>
       <layout class="QGridLayout" name="gridLayout_4">
        <property name="topMargin">
         <number>4</number>
        </property>
        <property name="bottomMargin">
         <number>4</number>
        </property>
        <property name="verticalSpacing">
         <number>4</number>
        </property>
        <item row="5" column="0">
         <widget class="QPushButton" name="m_startColorButton">
          <property name="toolTip">
           <string>Matrix start color</string>
          </property>
          <property name="iconSize">
           <size>
            <width>50</width>
            <height>26</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="5" column="1">
         <widget class="QPushButton" name="m_endColorButton">
          <property name="toolTip">
           <string>Matrix end color</string>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="iconSize">
           <size>
            <width>50</width>
            <height>26</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="0" column="0" colspan="3">
         <widget class="QComboBox" name="m_patternCombo">
          <property name="toolTip">
           <string>The RGB matrix pattern</string>
          </property>
         </widget>
        </item>
        <item row="5" column="2">
         <widget class="QToolButton" name="m_resetEndColorButton">
          <property name="toolTip">
           <string>Reset the end color</string>
          </property>
          <property name="icon">
           <iconset resource="qlcui.qrc">
            <normaloff>:/fileclose.png</normaloff>:/fileclose.png</iconset>
          </property>
         </widget>
        </item>
        <item row="3" column="1" colspan="2">
         <widget class="QComboBox" name="m_blendModeCombo">
          <item>
           <property name="text">
            <string>Default (HTP)</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Mask</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Additive</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Subtractive</string>
           </property>
          </item>
         </widget>
        </item>
        <item row="3" column="0">
         <widget class="QLabel" name="m_blendModeLabel">
          <property name="text">
           <string>Blend mode</string>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QLabel" name="m_colorModeLabel">
          <property name="text">
           <string>Control mode</string>
          </property>
         </widget>
        </item>
        <item row="2" column="1" colspan="2">
         <widget class="QComboBox" name="m_controlModeCombo">
          <item>
           <property name="text">
            <string>Default (RGB)</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>White</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Amber</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>UV</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Dimmer</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Shutter</string>
           </property>
          </item>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="m_propertiesGroup">
       <property name="title">
        <string>Properties</string>
       </property>
       <layout class="QGridLayout" name="gridLayout_5">
        <property name="topMargin">
         <number>4</number>
        </property>
        <property name="bottomMargin">
         <number>4</number>
        </property>
        <property name="verticalSpacing">
         <number>4</number>
        </property>
        <item row="0" column="0">
         <layout class="QGridLayout" name="m_propertiesLayout"/>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="m_textGroup">
       <property name="title">
        <string>Animated Text</string>
       </property>
       <layout class="QGridLayout" name="gridLayout_3">
        <property name="topMargin">
         <number>4</number>
        </property>
        <property name="bottomMargin">
         <number>4</number>
        </property>
        <property name="verticalSpacing">
         <number>4</number>
        </property>
        <item row="0" column="0">
         <widget class="QLineEdit" name="m_textEdit">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="toolTip">
           <string>Text to display</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QToolButton" name="m_fontButton">
          <property name="toolTip">
           <string>Choose the font</string>
          </property>
          <property name="text">
           <string notr="true"/>
          </property>
          <property name="icon">
           <iconset resource="qlcui.qrc">
            <normaloff>:/fonts.png</normaloff>:/fonts.png</iconset>
          </property>
          <property name="iconSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QComboBox" name="m_animationCombo">
          <property name="toolTip">
           <string>Animation style</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="m_imageGroup">
       <property name="title">
        <string>Image</string>
       </property>
       <layout class="QGridLayout" name="gridLayout_2">
        <property name="topMargin">
         <number>4</number>
        </property>
        <property name="bottomMargin">
         <number>4</number>
        </property>
        <property name="verticalSpacing">
         <number>4</number>
        </property>
        <item row="0" column="0">
         <widget class="QLineEdit" name="m_imageEdit">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QToolButton" name="m_imageButton">
          <property name="text">
           <string notr="true"/>
          </property>
          <property name="icon">
           <iconset resource="qlcui.qrc">
            <normaloff>:/image.png</normaloff>:/image.png</iconset>
          </property>
          <property name="iconSize">
           <size>
            <width>32</width>
            <height>32</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="1" column="0" colspan="2">
         <widget class="QComboBox" name="m_imageAnimationCombo"/>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="m_offsetGroup">
       <property name="title">
        <string>Offset</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout">
        <property name="topMargin">
         <number>4</number>
        </property>
        <property name="bottomMargin">
         <number>4</number>
        </property>
        <item>
         <widget class="QLabel" name="m_xOffsetLabel">
          <property name="text">
           <string>X</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QSpinBox" name="m_xOffsetSpin">
          <property name="toolTip">
           <string>Shift the pattern X pixels horizontally</string>
          </property>
          <property name="minimum">
           <number>-255</number>
          </property>
          <property name="maximum">
           <number>255</number>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="m_yOffsetLabel">
          <property name="text">
           <string>Y</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QSpinBox" name="m_yOffsetSpin">
          <property name="toolTip">
           <string>Shift the pattern Y pixels vertically</string>
          </property>
          <property name="minimum">
           <number>-255</number>
          </property>
          <property name="maximum">
           <number>255</number>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <spacer name="verticalSpacer_4">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeType">
        <enum>QSizePolicy::Expanding</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item row="4" column="3">
    <widget class="QGroupBox" name="m_intensityGroup">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="title">
      <string>Other Controls</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_4">
      <property name="topMargin">
       <number>4</number>
      </property>
      <property name="bottomMargin">
       <number>4</number>
      </property>
      <item>
       <widget class="QCheckBox" name="m_dimmerControlCb">
        <property name="toolTip">
         <string>Set the dimmer channel of fixtures to 100%</string>
        </property>
        <property name="text">
         <string>Dimmer control</string>
        </property>
       </widget>
      </item>

     </layout>
    </widget>
   </item>
   <item row="0" column="0" colspan="4">
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QLabel" name="m_nameLabel">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string>RGB matrix name</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="m_nameEdit">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>The name of this RGB matrix function</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="0" colspan="4">
    <layout class="QHBoxLayout" name="horizontalLayout_4">
     <item>
      <widget class="QToolButton" name="m_speedDialButton">
       <property name="toolTip">
        <string>Show/Hide speed dial window</string>
       </property>
       <property name="icon">
        <iconset resource="qlcui.qrc">
         <normaloff>:/speed.png</normaloff>:/speed.png</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>28</width>
         <height>28</height>
        </size>
       </property>
       <property name="checkable">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="m_saveToSequenceButton">
       <property name="toolTip">
        <string>Save this matrix to a sequence</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="qlcui.qrc">
         <normaloff>:/sequence.png</normaloff>:/sequence.png</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>28</width>
         <height>28</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="m_shapeButton">
       <property name="toolTip">
        <string>Toggle between circle and square preview</string>
       </property>
       <property name="icon">
        <iconset resource="qlcui.qrc">
         <normaloff>:/square.png</normaloff>:/square.png</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>28</width>
         <height>28</height>
        </size>
       </property>
       <property name="checkable">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="m_testButton">
       <property name="toolTip">
        <string>See what the RGB Matrix does when it is run</string>
       </property>
       <property name="icon">
        <iconset resource="qlcui.qrc">
         <normaloff>:/player_play.png</normaloff>:/player_play.png</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>28</width>
         <height>28</height>
        </size>
       </property>
       <property name="checkable">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="m_fixtureGroupLabel">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string>Fixture group</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="m_fixtureGroupCombo">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>The fixture group to use as the pixel matrix</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="3" column="0" rowspan="2" colspan="3">
    <layout class="QGridLayout" name="gridLayout_6">
     <item row="0" column="0">
      <widget class="QGroupBox" name="m_runOrderGroup">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="title">
        <string>Run Order</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <property name="topMargin">
         <number>4</number>
        </property>
        <property name="bottomMargin">
         <number>4</number>
        </property>
        <item>
         <widget class="QRadioButton" name="m_loop">
          <property name="toolTip">
           <string>Run through over and over again</string>
          </property>
          <property name="text">
           <string>Loop</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QRadioButton" name="m_singleShot">
          <property name="toolTip">
           <string>Run through once and stop</string>
          </property>
          <property name="text">
           <string>Single Shot</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QRadioButton" name="m_pingPong">
          <property name="toolTip">
           <string>First run forwards, then backwards, again forwards, etc.</string>
          </property>
          <property name="text">
           <string>Ping Pong</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QGroupBox" name="m_directionGroup">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="title">
        <string>Direction</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_3">
        <property name="topMargin">
         <number>4</number>
        </property>
        <property name="bottomMargin">
         <number>4</number>
        </property>
        <item>
         <widget class="QRadioButton" name="m_forward">
          <property name="toolTip">
           <string>Start from the first step</string>
          </property>
          <property name="text">
           <string>Forward</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QRadioButton" name="m_backward">
          <property name="toolTip">
           <string>Start from the last step</string>
          </property>
          <property name="text">
           <string>Backward</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="verticalSpacer_2">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Maximum</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>35</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="qlcui.qrc"/>
 </resources>
 <connections/>
</ui>
