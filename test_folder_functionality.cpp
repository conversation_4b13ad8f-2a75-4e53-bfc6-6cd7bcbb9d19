#include <QApplication>
#include <QDebug>
#include <QList>
#include <QMap>
#include <QString>
#include <QFile>
#include <QXmlStreamReader>

// Include QLC+ headers
#include "engine/src/doc.h"
#include "engine/src/fixturegroup.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    // Create a document and manually create test fixture groups
    Doc doc(nullptr);

    qDebug() << "Creating test fixture groups with folders...";

    // Create test fixture groups with folders
    FixtureGroup* group1 = new FixtureGroup(&doc);
    group1->setName("Stage Left");
    group1->setFolder("Stage Lighting");
    group1->setId(0);
    doc.addFixtureGroup(group1);

    FixtureGroup* group2 = new FixtureGroup(&doc);
    group2->setName("Stage Right");
    group2->setFolder("Stage Lighting");
    group2->setId(1);
    doc.addFixtureGroup(group2);

    FixtureGroup* group3 = new FixtureGroup(&doc);
    group3->setName("House Lights");
    group3->setFolder("House Lighting");
    group3->setId(2);
    doc.addFixtureGroup(group3);

    FixtureGroup* group4 = new FixtureGroup(&doc);
    group4->setName("All Dimmers");
    group4->setFolder(""); // No folder
    group4->setId(3);
    doc.addFixtureGroup(group4);

    qDebug() << "Successfully created test fixture groups";
    
    // Get all fixture groups
    QList<FixtureGroup*> groups = doc.fixtureGroups();
    qDebug() << "Found" << groups.size() << "fixture groups";
    
    // Organize groups by folder
    QMap<QString, QList<FixtureGroup*>> groupsByFolder;
    
    for (FixtureGroup* group : groups)
    {
        QString folder = group->folder().isEmpty() ? QString("(No Folder)") : group->folder();
        groupsByFolder[folder].append(group);
        qDebug() << "Group:" << group->name() << "Folder:" << folder;
    }
    
    qDebug() << "\n=== Folder Organization ===";
    
    // Display folder hierarchy
    QStringList folderKeys = groupsByFolder.keys();
    folderKeys.sort();
    
    for (const QString &folder : folderKeys)
    {
        qDebug() << "Folder:" << folder;
        for (FixtureGroup* group : groupsByFolder[folder])
        {
            qDebug() << "  - Group:" << group->name() << "ID:" << group->id();
        }
    }
    
    // Test expected results
    bool testPassed = true;
    
    // Check if we have the expected folders
    if (!groupsByFolder.contains("Stage Lighting"))
    {
        qDebug() << "ERROR: Missing 'Stage Lighting' folder";
        testPassed = false;
    }
    else if (groupsByFolder["Stage Lighting"].size() != 2)
    {
        qDebug() << "ERROR: 'Stage Lighting' folder should contain 2 groups, found" << groupsByFolder["Stage Lighting"].size();
        testPassed = false;
    }
    
    if (!groupsByFolder.contains("House Lighting"))
    {
        qDebug() << "ERROR: Missing 'House Lighting' folder";
        testPassed = false;
    }
    else if (groupsByFolder["House Lighting"].size() != 1)
    {
        qDebug() << "ERROR: 'House Lighting' folder should contain 1 group, found" << groupsByFolder["House Lighting"].size();
        testPassed = false;
    }
    
    if (!groupsByFolder.contains("(No Folder)"))
    {
        qDebug() << "ERROR: Missing groups without folder";
        testPassed = false;
    }
    else if (groupsByFolder["(No Folder)"].size() != 1)
    {
        qDebug() << "ERROR: Should have 1 group without folder, found" << groupsByFolder["(No Folder)"].size();
        testPassed = false;
    }
    
    if (testPassed)
    {
        qDebug() << "\n✅ FOLDER FUNCTIONALITY TEST PASSED!";
        qDebug() << "Fixture groups are properly organized by folders.";
    }
    else
    {
        qDebug() << "\n❌ FOLDER FUNCTIONALITY TEST FAILED!";
        qDebug() << "Fixture groups are not properly organized by folders.";
        return 1;
    }
    
    return 0;
}
