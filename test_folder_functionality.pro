QT += core widgets
CONFIG += console
CONFIG -= app_bundle

TARGET = test_folder_functionality
TEMPLATE = app

SOURCES += test_folder_functionality.cpp

# Include paths
INCLUDEPATH += .
INCLUDEPATH += engine/src

# Link against QLC+ engine library
LIBS += -Lengine/src -lqlcplusengine.1.0.0

# macOS specific settings
macx {
    QMAKE_LFLAGS += -Wl,-rpath,@executable_path
    QMAKE_LFLAGS += -Wl,-rpath,@executable_path/engine/src
}
